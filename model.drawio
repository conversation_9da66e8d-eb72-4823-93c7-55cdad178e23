<mxfile host="65bd71144e">
    <diagram id="3LhUfXpbuAioFFC_PiZy" name="Page-1">
        <mxGraphModel dx="1414" dy="728" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="5" value="Client" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="30" y="210" width="140" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="id" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="5">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="name" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="5">
                    <mxGeometry y="60" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="email" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="5">
                    <mxGeometry y="90" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="adress" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="5">
                    <mxGeometry y="120" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="Colis" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="440" y="210" width="140" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="id" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="9">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="clientId" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="9">
                    <mxGeometry y="60" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="programId" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="9">
                    <mxGeometry y="90" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="name" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="9">
                    <mxGeometry y="120" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="description" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="9">
                    <mxGeometry y="150" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="PorgramVoyage" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="440" y="660" width="140" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="id" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="17">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="destinationId" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="17">
                    <mxGeometry y="60" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="date" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="17">
                    <mxGeometry y="90" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="78" value="startTrip" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="17">
                    <mxGeometry y="120" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="price" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="17">
                    <mxGeometry y="150" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="Driver" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="20" y="680" width="140" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="id" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="26">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="name" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="26">
                    <mxGeometry y="60" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="lastname" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="26">
                    <mxGeometry y="90" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="tel" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="26">
                    <mxGeometry y="120" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="Destination" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="940" y="630" width="140" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="id" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="34">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="nomVille" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="34">
                    <mxGeometry y="60" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="address" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="34">
                    <mxGeometry y="90" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="withdraw" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="920" y="210" width="140" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="id" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="48">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="clientId" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="48">
                    <mxGeometry y="60" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="name" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="48">
                    <mxGeometry y="90" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="email" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="48">
                    <mxGeometry y="120" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="adress" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="48">
                    <mxGeometry y="150" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="48" target="9">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="450" y="430" as="sourcePoint"/>
                        <mxPoint x="610" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="retirer" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="54">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="(1,1)" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;" connectable="0" vertex="1" parent="54">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="(1,n)" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;" connectable="0" vertex="1" parent="54">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="58" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="5" target="9">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="450" y="430" as="sourcePoint"/>
                        <mxPoint x="610" y="430" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="310" y="290"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="Depose" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="58">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="(1,1)" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;" connectable="0" vertex="1" parent="58">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="(1,n)" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;" connectable="0" vertex="1" parent="58">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="62" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="9" target="17">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="450" y="430" as="sourcePoint"/>
                        <mxPoint x="620" y="670" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="Concerne" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="62">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="(0,n)" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;" connectable="0" vertex="1" parent="62">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="(1,1)" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;" connectable="0" vertex="1" parent="62">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="26" target="17">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="190" y="800" as="sourcePoint"/>
                        <mxPoint x="340" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="71" value="Conduire" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="70">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="(1,1)" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;" connectable="0" vertex="1" parent="70">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="73" value="(1,n)" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;" connectable="0" vertex="1" parent="70">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="74" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="34" target="17">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="620" y="500" as="sourcePoint"/>
                        <mxPoint x="780" y="500" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" value="Etre" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" connectable="0" vertex="1" parent="74">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="76" value="(1,1)" style="edgeLabel;resizable=0;html=1;align=left;verticalAlign=bottom;" connectable="0" vertex="1" parent="74">
                    <mxGeometry x="-1" relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="77" value="(1,n)" style="edgeLabel;resizable=0;html=1;align=right;verticalAlign=bottom;" connectable="0" vertex="1" parent="74">
                    <mxGeometry x="1" relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>