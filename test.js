const {
  mouse,
  keyboard,
  screen,
  Key,
  straightTo,
  Point,
} = require("@nut-tree-fork/nut-js");

function getRandomDelay(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function getSlightlyRandomPosition(currentX, currentY) {
  const screenWidth = await screen.width();
  const screenHeight = await screen.height();
  const delta = 100; // Déplacement léger pour rester naturel

  const x = Math.min(
    Math.max(currentX + Math.floor(Math.random() * delta * 2 - delta), 0),
    screenWidth - 1
  );

  const y = Math.min(
    Math.max(currentY + Math.floor(Math.random() * delta * 2 - delta), 0),
    screenHeight - 1
  );

  return { x, y };
}

async function moveMouseNaturally() {
  const currentPos = await mouse.getPosition();
  const { x, y } = await getSlightlyRandomPosition(currentPos.x, currentPos.y);

  // Move mouse to new position
  await mouse.move(straightTo(new Point(x, y)));

  // <PERSON><PERSON><PERSON>, on interagit avec VSCode
  if (Math.random() > 0.7) await scrollInVSCode();
  if (Math.random() > 0.85) await changeFileInVSCode();
  if (Math.random() > 0.9) await switchVSCodeTab();

  // On attend un temps aléatoire entre 1 et 5 sec avant de bouger encore
  const nextDelay = getRandomDelay(700, 2000);
  setTimeout(moveMouseNaturally, nextDelay);
}

async function scrollInVSCode() {
  const direction = Math.random() > 0.5 ? 1 : -1;
  if (direction > 0) {
    await mouse.scrollDown(5);
  } else {
    await mouse.scrollUp(5);
  }
}

async function changeFileInVSCode() {
  const direction = Math.random() > 0.5 ? "right" : "left";
  if (direction === "right") {
    await keyboard.pressKey(Key.LeftControl, Key.Right);
  } else {
    await keyboard.pressKey(Key.LeftControl, Key.Left);
  }
}

async function switchVSCodeTab() {
  await keyboard.pressKey(Key.LeftControl, Key.Tab);
}

// 🟢 Lancement
(async () => {
  await moveMouseNaturally();
})();
