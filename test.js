const robot = require("robotjs");

function getRandomDelay(min = 1000, max = 5000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getSlightlyRandomPosition(currentX, currentY) {
  const screenSize = robot.getScreenSize();
  const delta = 100; // Déplacement léger pour rester naturel

  const x = Math.min(
    Math.max(currentX + Math.floor(Math.random() * delta * 2 - delta), 0),
    screenSize.width - 1
  );

  const y = Math.min(
    Math.max(currentY + Math.floor(Math.random() * delta * 2 - delta), 0),
    screenSize.height - 1
  );

  return { x, y };
}

function moveMouseNaturally() {
  const currentPos = robot.getMousePos();
  const { x, y } = getSlightlyRandomPosition(currentPos.x, currentPos.y);
  const speed = Math.random() * 1.2 + 0.3; // Plus lent et fluide

  robot.moveMouseSmooth(x, y, speed);

  // <PERSON>rfois, on interagit avec VSCode
  if (Math.random() > 0.7) scrollInVSCode();
  if (Math.random() > 0.85) changeFileInVSCode();
  if (Math.random() > 0.9) switchVSCodeTab();

  // On attend un temps aléatoire entre 1 et 5 sec avant de bouger encore
  const nextDelay = getRandomDelay(700, 2000);
  setTimeout(moveMouseNaturally, nextDelay);
}

function scrollInVSCode() {
  const direction = Math.random() > 0.5 ? 1 : -1;
  robot.scrollMouse(0, direction * 5);
}

function changeFileInVSCode() {
  const direction = Math.random() > 0.5 ? "right" : "left";
  robot.keyTap(direction, "control");
}

function switchVSCodeTab() {
  robot.keyTap("tab", "control");
}

// 🟢 Lancement
moveMouseNaturally();

